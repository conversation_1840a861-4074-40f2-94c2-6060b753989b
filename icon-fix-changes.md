# Icon Path Usage Analysis and Production Build Considerations

## Summary
This document lists all files that use dynamic icon path bindings or icon references that may need attention for production builds.

## Files with Dynamic Icon Path Bindings

### 1. Components with Ternary Operators for Icon Selection

#### `/src/components/timer/PomodoroTimer.vue`
- **Line 49**: `:src="timerStore.isRunning ? pauseIcon : playIcon"`
- Uses imported icon modules (correctly handled)

#### `/src/components/dashboard/RecentActivity.vue`
- **Line 19**: `:src="item.type === 'note' ? '/icons/notes-icon.svg' : '/icons/books-icon.svg'"`
- Uses direct path strings (potential issue in production)

#### `/src/components/modals/AddBookManuallyModal.vue`
- **Line 82**: `:src="star <= formData.rating ? '/icons/star-filled-icon.svg' : '/icons/star-icon.svg'"`
- Uses direct path strings (potential issue in production)

#### `/src/components/modals/BookDetailsModal.vue`
- **Line 155**: `:src="star <= currentRating ? '/icons/star-filled-icon.svg' : '/icons/star-icon.svg'"`
- Uses direct path strings (potential issue in production)

#### `/src/components/modals/DeleteMixedItemsModal.vue`
- **Line 27**: `:src="item.type === 'folder' ? '/icons/folder-icon.svg' : '/icons/note-icon.svg'"`
- Uses direct path strings (potential issue in production)

#### `/src/components/modals/EditBookModal.vue`
- **Line 101**: `:src="star <= formData.rating ? '/icons/star-filled-icon.svg' : '/icons/star-icon.svg'"`
- Uses direct path strings (potential issue in production)

### 2. Components with Static Icon References

#### `/src/components/folders/SingleFolder.vue`
- **Line 44**: `src="/icons/dropdown-arrow-icon.svg"`
- Static reference (should work in production)

### 3. Components with CSS Background Images

#### `/src/components/notes/NoteEditor.vue`
Multiple CSS background-image declarations using icon paths:
- Lines 1091-1364: Various toolbar button icons using `url('/icons/...')` patterns
- Examples:
  - `background: url('/icons/bold-icon.svg') no-repeat center center;`
  - `background: url('/icons/italic-icon.svg') no-repeat center center;`
  - `background-image: url('/icons/font-icon.svg');`
  - And many more...

## Files with Correct Icon Import Patterns

These files correctly import icons as modules, which should work properly in production:

#### `/src/components/timer/PomodoroTimer.vue`
```javascript
import settingsIcon from '/icons/settings-icon.svg'
import restartIcon from '/icons/restart-icon.svg'
import playIcon from '/icons/play-icon.svg'
import pauseIcon from '/icons/pause-icon.svg'
import skipIcon from '/icons/skip-icon.svg'
```

#### `/src/components/common/TitleBar.vue`
```javascript
import dashboardIcon from '/icons/dashboard-icon.svg'
import notesIcon from '/icons/notes-icon.svg'
import booksIcon from '/icons/books-icon.svg'
import foldersIcon from '/icons/folders-icon.svg'
import timerIcon from '/icons/timer-icon.svg'
import settingsIcon from '/icons/settings-icon.svg'
```

#### `/src/components/folders/FolderNavigator.vue`
```javascript
import notesIcon from '/icons/notes-icon.svg'
import fileIcon from '/icons/file-icon.svg'
import renameIcon from '/icons/rename-icon.svg'
import dropdownArrowIcon from '/icons/dropdown-arrow-icon.svg'
```

## Potential Issues in Production

### 1. Direct Path Strings in Templates
Files that use direct path strings in ternary operators (like `'/icons/star-icon.svg'`) may not work correctly in production builds because:
- The paths might not be properly resolved during the build process
- The icons might not be included in the final bundle

### 2. CSS Background Images
The `NoteEditor.vue` component has many CSS rules with `url('/icons/...')` patterns which may need special handling for production builds.

## Recommendations

1. **For Dynamic Icon Selection**: Convert direct path strings to imported modules
   ```javascript
   // Instead of:
   :src="condition ? '/icons/icon1.svg' : '/icons/icon2.svg'"
   
   // Use:
   import icon1 from '/icons/icon1.svg'
   import icon2 from '/icons/icon2.svg'
   // Then:
   :src="condition ? icon1 : icon2"
   ```

2. **For CSS Background Images**: Consider using CSS variables or inline styles with imported icons
   ```javascript
   // Import the icon
   import boldIcon from '/icons/bold-icon.svg'
   
   // Use in template with inline style
   :style="{ backgroundImage: `url(${boldIcon})` }"
   ```

3. **Test Production Build**: Always test the production build to ensure all icons are properly loaded and displayed.