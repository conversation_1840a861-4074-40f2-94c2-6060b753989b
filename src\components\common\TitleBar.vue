<template>
  <div class="title-bar">
    <!-- Timer display on left -->
    <div class="left-section">
      <div 
        v-if="showTimer && timerStore.sessionActive" 
        class="title-timer"
        :class="{ 
          'timer-running': timerStore.isRunning,
          'timer-paused': !timerStore.isRunning,
          'timer-break': timerStore.timerType !== 'pomodoro'
        }"
      >
        <div class="timer-icon">
          <svg v-if="timerStore.isRunning" width="8" height="8" viewBox="0 0 8 8">
            <circle cx="4" cy="4" r="3" fill="currentColor" />
          </svg>
          <svg v-else width="8" height="8" viewBox="0 0 8 8">
            <rect x="2" y="1" width="1.5" height="6" fill="currentColor" />
            <rect x="4.5" y="1" width="1.5" height="6" fill="currentColor" />
          </svg>
        </div>
        
        <span class="timer-time">{{ timerStore.formattedTime }}</span>
        
        <span 
          v-if="settingsStore.titleBarTimerFormat === 'detailed'" 
          class="timer-type"
        >
          {{ formatCycleType(timerStore.timerType) }}
        </span>
      </div>
    </div>
    
    <!-- Current route in center -->
    <div class="current-route">
      <img v-if="currentIcon" :src="currentIcon" class="route-icon" alt="" />
      <span class="route-name">{{ currentRouteName }}</span>
    </div>
    
    <!-- Window controls on right -->
    <div class="window-controls">
      <button @click="minimizeWindow" class="control-button minimize" title="Minimize">
        <svg width="10" height="1" viewBox="0 0 10 1">
          <path d="M0 0h10v1H0z" fill="currentColor" />
        </svg>
      </button>
      <button @click="maximizeWindow" class="control-button maximize" title="Maximize">
        <svg v-if="!isMaximized" width="10" height="10" viewBox="0 0 10 10">
          <path d="M0 0v10h10V0H0zm9 9H1V1h8v8z" fill="currentColor" />
        </svg>
        <svg v-else width="10" height="10" viewBox="0 0 10 10">
          <path d="M2.1,0v2H0v8.1h8.2v-2h2V0H2.1z M7.2,9.2H1.1V3h6.1V9.2z M9.2,7.1h-1V2H3.1V1h6.1V7.1z" fill="currentColor" />
        </svg>
      </button>
      <button @click="closeWindow" class="control-button close" title="Close">
        <svg width="10" height="10" viewBox="0 0 10 10">
          <path d="M10 1.01L6.01 5 10 8.99 8.99 10 5 6.01 1.01 10 0 8.99 3.99 5 0 1.01 1.01 0 5 3.99 8.99 0 10 1.01z" fill="currentColor" />
        </svg>
      </button>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue'
import { useTimerStore } from '../../stores/timerStore'
import { useSettingsStore } from '../../stores/settingsStore'

// Import icons as modules for proper path resolution in production
import dashboardIcon from '/icons/dashboard-icon.svg'
import notesIcon from '/icons/notes-icon.svg'
import booksIcon from '/icons/books-icon.svg'
import foldersIcon from '/icons/folders-icon.svg'
import timerIcon from '/icons/timer-icon.svg'
import settingsIcon from '/icons/settings-icon.svg'

export default defineComponent({
  name: 'TitleBar',
  setup() {
    const timerStore = useTimerStore()
    const settingsStore = useSettingsStore()
    
    const routes = [
      { path: '/', name: 'Dashboard', icon: dashboardIcon },
      { path: '/notes', name: 'Notes', icon: notesIcon },
      { path: '/books', name: 'Books', icon: booksIcon },
      { path: '/folders', name: 'Folders', icon: foldersIcon },
      { path: '/timer', name: 'Timer', icon: timerIcon },
      { path: '/settings', name: 'Settings', icon: settingsIcon }
    ]
    
    return {
      timerStore,
      settingsStore,
      routes,
      isMaximized: false
    }
  },
  computed: {
    currentRoute() {
      return this.$route.path
    },
    currentRouteName() {
      const route = this.routes.find(r => r.path === this.currentRoute)
      return route ? route.name : 'Noti'
    },
    currentIcon() {
      const route = this.routes.find(r => r.path === this.currentRoute)
      return route ? route.icon : null
    },
    showTimer() {
      return this.settingsStore.showTimerInTitleBar
    }
  },
  methods: {
    formatCycleType(type: string): string {
      switch (type) {
        case 'pomodoro':
          return 'Focus'
        case 'shortBreak':
          return 'Short Break'
        case 'longBreak':
          return 'Long Break'
        default:
          return 'Timer'
      }
    },
    async minimizeWindow() {
      await window.ipcRenderer.invoke('window:minimize')
    },
    async maximizeWindow() {
      const result = await window.ipcRenderer.invoke('window:maximize')
      this.isMaximized = result
    },
    async closeWindow() {
      await window.ipcRenderer.invoke('window:close')
    }
  }
})
</script>

<style scoped>
.title-bar {
  height: 32px;
  width: 100%;
  background-color: var(--color-nav-bg);
  -webkit-app-region: drag; /* Makes the entire title bar draggable */
  display: flex;
  justify-content: space-between; /* Space elements evenly */
  align-items: center;
  padding: 0;
  position: relative;
  border-bottom: 1px solid var(--color-nav-border); /* Bottom separator line matching sidebar style */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.left-section {
  display: flex;
  align-items: center;
  min-width: 138px; /* Match width of window controls to balance layout */
  padding-left: 12px;
}

.current-route {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  font-weight: 500;
  color: var(--color-text-primary);
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.route-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

/* ===== TIMER IN TITLE BAR STYLES ===== */
.title-timer {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  transition: all 0.2s ease;
  background: var(--color-nav-item-active);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border-primary);
}

.title-timer.timer-running {
  background: var(--color-nav-item-active);
  color: var(--color-text-secondary);
  border-color: var(--color-border-hover);
}

.title-timer.timer-paused {
  background: var(--color-nav-item-active);
  color: var(--color-text-tertiary);
  border-color: var(--color-border-hover);
}

.title-timer.timer-break {
  background: var(--color-nav-item-active);
  color: var(--color-text-muted);
  border-color: var(--color-border-hover);
}

.timer-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 8px;
  height: 8px;
}

.timer-running .timer-icon {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.timer-time {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  letter-spacing: 0.5px;
}

.timer-type {
  font-size: 10px;
  opacity: 0.8;
  margin-left: 2px;
}

/* ===== WINDOW CONTROLS ===== */
.window-controls {
  display: flex;
  -webkit-app-region: no-drag; /* Make controls clickable */
}

.control-button {
  width: 46px;
  height: 32px;
  border: none;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--color-text-secondary);
  outline: none;
}

.control-button:hover {
  background-color: var(--color-nav-item-hover);
}

.minimize:hover,
.maximize:hover {
  color: var(--color-text-primary);
}

.close:hover {
  background-color: var(--color-window-close-hover);
  color: var(--color-window-close-text);
}
</style>